package br.ufs.sicad.infrastructure.repositories;

import br.ufs.sicad.domain.entidades.NotaEmpenho;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Optional;

@Repository
public interface NotaEmpenhoRepository extends JpaRepository<NotaEmpenho, Long> {
    
    Optional<NotaEmpenho> findByNumero(String numero);
    
    boolean existsByNumero(String numero);
    
    @Query("SELECT n FROM NotaEmpenho n WHERE " +
           "(:numero IS NULL OR n.numero LIKE %:numero%) AND " +
           "(:dataEmissaoInicio IS NULL OR n.dataEmissao >= :dataEmissaoInicio) AND " +
           "(:dataEmissaoFim IS NULL OR n.dataEmissao <= :dataEmissaoFim) AND " +
           "(:valorMinimo IS NULL OR n.valorTotal >= :valorMinimo) AND " +
           "(:valorMaximo IS NULL OR n.valorTotal <= :valorMaximo)")
    Page<NotaEmpenho> findByFilters(
            @Param("numero") String numero,
            @Param("dataEmissaoInicio") LocalDate dataEmissaoInicio,
            @Param("dataEmissaoFim") LocalDate dataEmissaoFim,
            @Param("valorMinimo") BigDecimal valorMinimo,
            @Param("valorMaximo") BigDecimal valorMaximo,
            Pageable pageable);
    
    @Query("SELECT n FROM NotaEmpenho n WHERE n.prazoMaximoEntrega <= :dataLimite")
    Page<NotaEmpenho> findByPrazoMaximoEntregaLessThanEqual(
            @Param("dataLimite") LocalDate dataLimite, 
            Pageable pageable);
}
