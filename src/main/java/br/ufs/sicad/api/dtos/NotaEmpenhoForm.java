package br.ufs.sicad.api.dtos;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;

public record NotaEmpenhoForm(
        @NotBlank(message = "O número é obrigatório")
        @Size(max = 50, message = "O número deve ter no máximo 50 caracteres")
        String numero,
        
        @NotNull(message = "A data de emissão é obrigatória")
        String dataEmissao,
        
        @NotNull(message = "O valor total é obrigatório")
        @Positive(message = "O valor total deve ser positivo")
        BigDecimal valorTotal,
        
        @NotNull(message = "O prazo máximo de entrega é obrigatório")
        String prazoMaximoEntrega,
        
        @NotNull(message = "Os dias de notificação prévia são obrigatórios")
        @Positive(message = "Os dias de notificação prévia devem ser positivos")
        Integer diasNotificacaoPrevia
) {
}
