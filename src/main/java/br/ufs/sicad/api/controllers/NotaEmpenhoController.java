package br.ufs.sicad.api.controllers;

import br.ufs.sicad.api.dtos.NotaEmpenhoDTO;
import br.ufs.sicad.api.dtos.NotaEmpenhoForm;
import br.ufs.sicad.domain.entidades.NotaEmpenho;
import br.ufs.sicad.services.NotaEmpenhoService;
import br.ufs.sicad.utils.Utils;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;

@RestController
@RequestMapping("/notas-empenho")
@RequiredArgsConstructor
public class NotaEmpenhoController {

    private final NotaEmpenhoService service;

    @GetMapping
    public ResponseEntity<Page<NotaEmpenhoDTO>> listarTodos(
            @RequestParam(required = false) String numero,
            @RequestParam(required = false) String dataEmissaoInicio,
            @RequestParam(required = false) String dataEmissaoFim,
            @RequestParam(required = false) BigDecimal valorMinimo,
            @RequestParam(required = false) BigDecimal valorMaximo,
            Pageable pageable) {
        
        LocalDate dataInicio = dataEmissaoInicio != null ? Utils.converter(dataEmissaoInicio) : null;
        LocalDate dataFim = dataEmissaoFim != null ? Utils.converter(dataEmissaoFim) : null;
        
        Page<NotaEmpenho> notasEmpenho = service.listarComFiltros(numero, dataInicio, dataFim, 
                                                                 valorMinimo, valorMaximo, pageable);
        Page<NotaEmpenhoDTO> dtos = notasEmpenho.map(NotaEmpenhoDTO::from);
        return ResponseEntity.ok(dtos);
    }

    @GetMapping("/{id}")
    public ResponseEntity<NotaEmpenhoDTO> buscarPorId(@PathVariable Long id) {
        NotaEmpenho notaEmpenho = service.buscarPorId(id);
        return ResponseEntity.ok(NotaEmpenhoDTO.from(notaEmpenho));
    }

    @PostMapping
    public ResponseEntity<NotaEmpenhoDTO> criar(@RequestBody @Valid NotaEmpenhoForm form) {
        LocalDate dataEmissao = Utils.converter(form.dataEmissao());
        LocalDate prazoMaximoEntrega = Utils.converter(form.prazoMaximoEntrega());
        
        NotaEmpenho notaEmpenho = service.criar(
                form.numero(),
                dataEmissao,
                form.valorTotal(),
                prazoMaximoEntrega,
                form.diasNotificacaoPrevia()
        );
        
        return ResponseEntity.status(HttpStatus.CREATED).body(NotaEmpenhoDTO.from(notaEmpenho));
    }

    @PutMapping("/{id}")
    public ResponseEntity<NotaEmpenhoDTO> atualizar(@PathVariable Long id, 
                                                   @RequestBody @Valid NotaEmpenhoForm form) {
        LocalDate dataEmissao = Utils.converter(form.dataEmissao());
        LocalDate prazoMaximoEntrega = Utils.converter(form.prazoMaximoEntrega());
        
        NotaEmpenho notaEmpenho = service.atualizar(
                id,
                form.numero(),
                dataEmissao,
                form.valorTotal(),
                prazoMaximoEntrega,
                form.diasNotificacaoPrevia()
        );
        
        return ResponseEntity.ok(NotaEmpenhoDTO.from(notaEmpenho));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletar(@PathVariable Long id) {
        service.deletar(id);
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/{id}/anular-total")
    public ResponseEntity<Void> anularEmpenhoTotal(@PathVariable Long id) {
        service.anularEmpenhoTotal(id);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/{id}/anular-parcial")
    public ResponseEntity<Void> anularEmpenhoTotal(@PathVariable Long id, 
                                                  @RequestParam BigDecimal valor) {
        service.anularEmpenhoTotal(id, valor);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/vencimento")
    public ResponseEntity<Page<NotaEmpenhoDTO>> buscarPorPrazoVencimento(
            @RequestParam String dataLimite,
            Pageable pageable) {
        
        LocalDate data = Utils.converter(dataLimite);
        Page<NotaEmpenho> notasEmpenho = service.buscarPorPrazoVencimento(data, pageable);
        Page<NotaEmpenhoDTO> dtos = notasEmpenho.map(NotaEmpenhoDTO::from);
        return ResponseEntity.ok(dtos);
    }

    // Endpoints específicos para getters e setters conforme o diagrama
    @GetMapping("/{id}/numero")
    public ResponseEntity<String> getNumero(@PathVariable Long id) {
        return ResponseEntity.ok(service.getNumero(id));
    }

    @PutMapping("/{id}/numero")
    public ResponseEntity<Void> setNumero(@PathVariable Long id, @RequestParam String numero) {
        service.setNumero(id, numero);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{id}/data-emissao")
    public ResponseEntity<LocalDate> getDataEmissao(@PathVariable Long id) {
        return ResponseEntity.ok(service.getDataEmissao(id));
    }

    @PutMapping("/{id}/data-emissao")
    public ResponseEntity<Void> setDataEmissao(@PathVariable Long id, @RequestParam String data) {
        service.setDataEmissao(id, Utils.converter(data));
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{id}/valor-total")
    public ResponseEntity<BigDecimal> getValorTotal(@PathVariable Long id) {
        return ResponseEntity.ok(service.getValorTotal(id));
    }

    @PutMapping("/{id}/valor-total")
    public ResponseEntity<Void> setValorTotal(@PathVariable Long id, @RequestParam BigDecimal valor) {
        service.setValorTotal(id, valor);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{id}/prazo-maximo-entrega")
    public ResponseEntity<LocalDate> getPrazoMaximoEntrega(@PathVariable Long id) {
        return ResponseEntity.ok(service.getPrazoMaximoEntrega(id));
    }

    @PutMapping("/{id}/prazo-maximo-entrega")
    public ResponseEntity<Void> setPrazoMaximoEntrega(@PathVariable Long id, @RequestParam String data) {
        service.setPrazoMaximoEntrega(id, Utils.converter(data));
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{id}/dias-notificacao-previa")
    public ResponseEntity<Integer> getDiasNotificacaoPrevia(@PathVariable Long id) {
        return ResponseEntity.ok(service.getDiasNotificacaoPrevia(id));
    }

    @PutMapping("/{id}/dias-notificacao-previa")
    public ResponseEntity<Void> setDiasNotificacaoPrevia(@PathVariable Long id, @RequestParam Integer dias) {
        service.setDiasNotificacaoPrevia(id, dias);
        return ResponseEntity.ok().build();
    }
}
