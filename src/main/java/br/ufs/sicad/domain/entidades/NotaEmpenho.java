package br.ufs.sicad.domain.entidades;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "nota_empenho")
public class NotaEmpenho {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "O número é obrigatório")
    @Size(max = 50, message = "O número deve ter no máximo 50 caracteres")
    @Column(name = "numero", nullable = false, unique = true, length = 50)
    private String numero;

    @NotNull(message = "A data de emissão é obrigatória")
    @Column(name = "data_emissao", nullable = false)
    private LocalDate dataEmissao;

    @NotNull(message = "O valor total é obrigatório")
    @Positive(message = "O valor total deve ser positivo")
    @Column(name = "valor_total", nullable = false, precision = 15, scale = 2)
    private BigDecimal valorTotal;

    @NotNull(message = "O prazo máximo de entrega é obrigatório")
    @Column(name = "prazo_maximo_entrega", nullable = false)
    private LocalDate prazoMaximoEntrega;

    @NotNull(message = "Os dias de notificação prévia são obrigatórios")
    @Positive(message = "Os dias de notificação prévia devem ser positivos")
    @Column(name = "dias_notificacao_previa", nullable = false)
    private Integer diasNotificacaoPrevia;

    // Construtor com parâmetros conforme o diagrama
    public NotaEmpenho(String numero, LocalDate dataEmissao, LocalDate prazoMaximoEntrega, Integer diasNotificacaoPrevia) {
        this.numero = numero;
        this.dataEmissao = dataEmissao;
        this.prazoMaximoEntrega = prazoMaximoEntrega;
        this.diasNotificacaoPrevia = diasNotificacaoPrevia;
    }

    // Método para anular empenho total conforme o diagrama
    public void anularEmpenhoTotal() {
        // Implementação da lógica de anulação total
        // Por enquanto, apenas um placeholder - pode ser expandido conforme regras de negócio
    }

    // Método para anular empenho parcial conforme o diagrama  
    public void anularEmpenhoTotal(BigDecimal valor) {
        // Implementação da lógica de anulação parcial
        // Por enquanto, apenas um placeholder - pode ser expandido conforme regras de negócio
        if (valor != null && valor.compareTo(BigDecimal.ZERO) > 0 && valor.compareTo(this.valorTotal) <= 0) {
            this.valorTotal = this.valorTotal.subtract(valor);
        }
    }
}
